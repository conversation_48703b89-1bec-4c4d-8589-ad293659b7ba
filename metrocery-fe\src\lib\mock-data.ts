export const mockProducts = [
  {
    id: "1",
    name: "Sữa tươi Vin<PERSON>lk",
    image:
      "https://cdn.tgdd.vn/Products/Images/2386/76520/bhx/sua-tuoi-tiet-trung-co-duong-vinamilk-100-sua-tuoi-hop-1-lit-202104100025450871.jpg",
    batch: "VM2023001",
    expiryDate: new Date("2024-12-31"),
    quantity: 100,
    category: "food",
    importPrice: 15000,
    sellingPrice: 18000,
    profitPercent: 20,
  },
  {
    id: "2",
    name: "<PERSON><PERSON><PERSON>",
    image:
      "https://cdn.tgdd.vn/Products/Images/2565/85959/bhx/mi-hao-hao-tom-chua-cay-goi-75g-202205161455061437.jpg",
    batch: "HH2023002",
    expiryDate: new Date("2024-10-15"),
    quantity: 200,
    category: "food",
    importPrice: 3500,
    sellingPrice: 4200,
    profitPercent: 20,
  },
  {
    id: "3",
    name: "<PERSON><PERSON><PERSON>c giặt Omo",
    image:
      "https://cdn.tgdd.vn/Products/Images/2464/76559/bhx/nuoc-giat-omo-matic-cua-tren-tui-3-6kg-202305271350080473.jpg",
    batch: "OM2023003",
    expiryDate: new Date("2025-06-30"),
    quantity: 50,
    category: "household",
    importPrice: 120000,
    sellingPrice: 150000,
    profitPercent: 25,
  },
  {
    id: "4",
    name: "Dầu gội Head & Shoulders",
    image:
      "https://cdn.tgdd.vn/Products/Images/2483/233371/bhx/dau-goi-head-shoulders-lam-sach-gau-va-ngua-chai-625ml-202304141359054372.jpg",
    batch: "HS2023004",
    expiryDate: new Date("2025-03-15"),
    quantity: 75,
    category: "personal",
    importPrice: 85000,
    sellingPrice: 102000,
    profitPercent: 20,
  },
  {
    id: "5",
    name: "Coca Cola",
    image:
      "https://cdn.tgdd.vn/Products/Images/2443/87880/bhx/nuoc-ngot-coca-cola-chai-390ml-202303160923085266.jpg",
    batch: "CC2023005",
    expiryDate: new Date("2024-09-20"),
    quantity: 150,
    category: "beverage",
    importPrice: 8000,
    sellingPrice: 10000,
    profitPercent: 25,
  },
];

export const mockEmployees = [
  {
    id: "1",
    fullName: "Nguyễn Văn A",
    email: "<EMAIL>",
    phone: "0123456789",
    role: "super_admin",
    avatar:
      "https://ui-avatars.com/api/?name=Nguyen+Van+A&background=0D8ABC&color=fff",
    joinDate: new Date("2023-01-01"),
    status: "active",
  },
  {
    id: "2",
    fullName: "Trần Thị B",
    email: "<EMAIL>",
    phone: "0987654321",
    role: "admin",
    avatar:
      "https://ui-avatars.com/api/?name=Tran+Thi+B&background=FF5733&color=fff",
    joinDate: new Date("2023-02-15"),
    status: "active",
  },
  {
    id: "3",
    fullName: "Lê Văn C",
    email: "<EMAIL>",
    phone: "0369852147",
    role: "staff",
    avatar:
      "https://ui-avatars.com/api/?name=Le+Van+C&background=27AE60&color=fff",
    joinDate: new Date("2023-03-20"),
    status: "active",
  },
  {
    id: "4",
    fullName: "Phạm Thị D",
    email: "<EMAIL>",
    phone: "0912345678",
    role: "staff",
    avatar:
      "https://ui-avatars.com/api/?name=Pham+Thi+D&background=8E44AD&color=fff",
    joinDate: new Date("2023-04-10"),
    status: "inactive",
  },
  {
    id: "5",
    fullName: "Hoàng Văn E",
    email: "<EMAIL>",
    phone: "0978563412",
    role: "staff",
    avatar:
      "https://ui-avatars.com/api/?name=Hoang+Van+E&background=F39C12&color=fff",
    joinDate: new Date("2023-05-05"),
    status: "active",
  },
];

export const mockCustomers = [
  {
    id: "1",
    fullName: "Nguyễn Thị Hương",
    email: "<EMAIL>",
    phone: "0912345678",
    address: "123 Nguyễn Huệ, Quận 1, TP.HCM",
    avatar:
      "https://ui-avatars.com/api/?name=Nguyen+Thi+Huong&background=4E9F3D&color=fff",
    joinDate: new Date("2023-01-15"),
    status: "active",
    totalOrders: 12,
    totalSpent: 5600000,
  },
  {
    id: "2",
    fullName: "Trần Văn Minh",
    email: "<EMAIL>",
    phone: "0987654321",
    address: "456 Lê Lợi, Quận 3, TP.HCM",
    avatar:
      "https://ui-avatars.com/api/?name=Tran+Van+Minh&background=1A5F7A&color=fff",
    joinDate: new Date("2023-02-20"),
    status: "active",
    totalOrders: 8,
    totalSpent: 3200000,
  },
  {
    id: "3",
    fullName: "Lê Thị Lan",
    email: "<EMAIL>",
    phone: "0978563412",
    address: "789 Trần Hưng Đạo, Quận 5, TP.HCM",
    avatar:
      "https://ui-avatars.com/api/?name=Le+Thi+Lan&background=FFC300&color=fff",
    joinDate: new Date("2023-03-10"),
    status: "inactive",
    totalOrders: 3,
    totalSpent: 1500000,
  },
  {
    id: "4",
    fullName: "Phạm Văn Đức",
    email: "<EMAIL>",
    phone: "0369852147",
    address: "101 Nguyễn Trãi, Quận 1, TP.HCM",
    avatar:
      "https://ui-avatars.com/api/?name=Pham+Van+Duc&background=FF5733&color=fff",
    joinDate: new Date("2023-04-05"),
    status: "active",
    totalOrders: 15,
    totalSpent: 7800000,
  },
  {
    id: "5",
    fullName: "Hoàng Thị Mai",
    email: "<EMAIL>",
    phone: "0912876543",
    address: "202 Võ Văn Tần, Quận 3, TP.HCM",
    avatar:
      "https://ui-avatars.com/api/?name=Hoang+Thi+Mai&background=8E44AD&color=fff",
    joinDate: new Date("2023-05-12"),
    status: "active",
    totalOrders: 6,
    totalSpent: 2800000,
  },
  {
    id: "6",
    fullName: "Vũ Đình Tùng",
    email: "<EMAIL>",
    phone: "0978123456",
    address: "303 Điện Biên Phủ, Quận Bình Thạnh, TP.HCM",
    avatar:
      "https://ui-avatars.com/api/?name=Vu+Dinh+Tung&background=2E86C1&color=fff",
    joinDate: new Date("2023-06-18"),
    status: "active",
    totalOrders: 10,
    totalSpent: 4500000,
  },
];

export const mockImports = [
  {
    id: "1",
    code: "PN001",
    importDate: new Date("2023-10-15"),
    supplierId: "supplier_1",
    supplierName: "Công ty TNHH Thực phẩm ABC",
    employeeId: "1",
    employeeName: "Nguyễn Văn A",
    items: [
      {
        id: "item_1",
        productId: "1",
        productName: "Sữa tươi Vinamilk",
        quantity: 50,
        importPrice: 15000,
        batch: "VM2023001",
        expiryDate: new Date("2024-12-31"),
        subtotal: 750000,
      },
      {
        id: "item_2",
        productId: "2",
        productName: "Mì gói Hảo Hảo",
        quantity: 100,
        importPrice: 3500,
        batch: "HH2023002",
        expiryDate: new Date("2024-10-15"),
        subtotal: 350000,
      },
    ],
    totalAmount: 1100000,
    status: "completed",
    note: "Nhập hàng đầy đủ, đúng số lượng",
  },
  {
    id: "2",
    code: "PN002",
    importDate: new Date("2023-11-05"),
    supplierId: "supplier_3",
    supplierName: "Công ty TNHH Đồ uống DEF",
    employeeId: "2",
    employeeName: "Trần Thị B",
    items: [
      {
        id: "item_3",
        productId: "5",
        productName: "Coca Cola",
        quantity: 100,
        importPrice: 8000,
        batch: "CC2023005",
        expiryDate: new Date("2024-09-20"),
        subtotal: 800000,
      },
    ],
    totalAmount: 800000,
    status: "completed",
    note: "Nhập hàng đúng hạn",
  },
  {
    id: "3",
    code: "PN003",
    importDate: new Date("2023-12-10"),
    supplierId: "supplier_4",
    supplierName: "Công ty TNHH Hàng tiêu dùng GHI",
    employeeId: "3",
    employeeName: "Lê Văn C",
    items: [
      {
        id: "item_4",
        productId: "3",
        productName: "Nước giặt Omo",
        quantity: 30,
        importPrice: 120000,
        batch: "OM2023003",
        expiryDate: new Date("2025-06-30"),
        subtotal: 3600000,
      },
      {
        id: "item_5",
        productId: "4",
        productName: "Dầu gội Head & Shoulders",
        quantity: 40,
        importPrice: 85000,
        batch: "HS2023004",
        expiryDate: new Date("2025-03-15"),
        subtotal: 3400000,
      },
    ],
    totalAmount: 7000000,
    status: "completed",
    note: "Nhập hàng đầy đủ",
  },
  {
    id: "4",
    code: "PN004",
    importDate: new Date("2024-01-20"),
    supplierId: "supplier_2",
    supplierName: "Công ty CP Thực phẩm XYZ",
    employeeId: "1",
    employeeName: "Nguyễn Văn A",
    items: [
      {
        id: "item_6",
        productId: "1",
        productName: "Sữa tươi Vinamilk",
        quantity: 80,
        importPrice: 15000,
        batch: "VM2024001",
        expiryDate: new Date("2025-01-31"),
        subtotal: 1200000,
      },
    ],
    totalAmount: 1200000,
    status: "processing",
    note: "Đang chờ kiểm tra chất lượng",
  },
  {
    id: "5",
    code: "PN005",
    importDate: new Date("2024-02-05"),
    supplierId: "supplier_5",
    supplierName: "Công ty CP Thực phẩm sạch JKL",
    employeeId: "2",
    employeeName: "Trần Thị B",
    items: [
      {
        id: "item_7",
        productId: "2",
        productName: "Mì gói Hảo Hảo",
        quantity: 150,
        importPrice: 3500,
        batch: "HH2024001",
        expiryDate: new Date("2025-02-15"),
        subtotal: 525000,
      },
    ],
    totalAmount: 525000,
    status: "cancelled",
    note: "Hủy do hàng không đạt chất lượng",
  },
];
