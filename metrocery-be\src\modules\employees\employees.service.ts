import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Employee } from './entities/employee.entity';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { QueryEmployeeDto } from './dto/query-employee.dto';

@Injectable()
export class EmployeesService {
  constructor(
    @InjectRepository(Employee)
    private readonly employeesRepository: Repository<Employee>,
  ) {}

  async create(createEmployeeDto: CreateEmployeeDto): Promise<Employee> {
    // Ki<PERSON>m tra email đã tồn tại
    const existingEmployee = await this.employeesRepository.findOne({
      where: { email: createEmployeeDto.email },
    });

    if (existingEmployee) {
      throw new ConflictException('Email already exists');
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(
      createEmployeeDto.password,
      saltRounds,
    );

    const employee = this.employeesRepository.create({
      ...createEmployeeDto,
      password: hashedPassword,
      passwordChangedAt: new Date(),
      joinDate: new Date(createEmployeeDto.joinDate),
    });

    return this.employeesRepository.save(employee);
  }

  async findAll(queryDto: QueryEmployeeDto) {
    const { page = 1, limit = 10, search } = queryDto;
    const skip = (page - 1) * limit;

    const queryBuilder =
      this.employeesRepository.createQueryBuilder('employee');

    // Tìm kiếm theo fullName, email, hoặc phone
    if (search) {
      queryBuilder.where(
        'employee.fullName LIKE :search OR employee.email LIKE :search OR employee.phone LIKE :search',
        { search: `%${search}%` },
      );
    }

    // Sắp xếp theo ngày tạo mới nhất
    queryBuilder.orderBy('employee.createdAt', 'DESC');

    // Phân trang
    queryBuilder.skip(skip).take(limit);

    const [employees, total] = await queryBuilder.getManyAndCount();

    return {
      data: employees,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<Employee> {
    const employee = await this.employeesRepository.findOne({ where: { id } });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${id} not found`);
    }

    return employee;
  }

  async update(
    id: string,
    updateEmployeeDto: UpdateEmployeeDto,
  ): Promise<Employee> {
    const employee = await this.findOne(id);

    // Kiểm tra email conflict nếu email được cập nhật
    if (updateEmployeeDto.email && updateEmployeeDto.email !== employee.email) {
      const existingEmployee = await this.employeesRepository.findOne({
        where: { email: updateEmployeeDto.email },
      });

      if (existingEmployee) {
        throw new ConflictException('Email already exists');
      }
    }

    // Cập nhật các fields
    if (updateEmployeeDto.fullName)
      employee.fullName = updateEmployeeDto.fullName;
    if (updateEmployeeDto.email) employee.email = updateEmployeeDto.email;
    if (updateEmployeeDto.phone) employee.phone = updateEmployeeDto.phone;
    if (updateEmployeeDto.avatar !== undefined)
      employee.avatar = updateEmployeeDto.avatar;
    if (updateEmployeeDto.role) employee.role = updateEmployeeDto.role;
    if (updateEmployeeDto.status) employee.status = updateEmployeeDto.status;
    if (updateEmployeeDto.joinDate) {
      employee.joinDate = new Date(updateEmployeeDto.joinDate);
    }
    return this.employeesRepository.save(employee);
  }

  async remove(id: string): Promise<{ success: boolean }> {
    const employee = await this.findOne(id);
    await this.employeesRepository.remove(employee);
    return { success: true };
  }

  // Authentication methods
  async findByEmail(email: string): Promise<Employee | null> {
    return this.employeesRepository.findOne({
      where: { email },
      select: [
        'id',
        'email',
        'password',
        'role',
        'status',
        'fullName',
        'loginAttempts',
        'lockedUntil',
        'lastLoginAt',
      ],
    });
  }

  async updateLoginAttempts(id: string, attempts: number): Promise<void> {
    await this.employeesRepository.update(id, { loginAttempts: attempts });
  }

  async updateLastLogin(id: string): Promise<void> {
    await this.employeesRepository.update(id, {
      lastLoginAt: new Date(),
      loginAttempts: 0,
    });
  }

  async lockAccount(id: string, lockUntil: Date): Promise<void> {
    await this.employeesRepository.update(id, {
      lockedUntil: lockUntil,
      loginAttempts: 0,
    });
  }

  async unlockAccount(id: string): Promise<void> {
    await this.employeesRepository.update(id, {
      lockedUntil: undefined,
      loginAttempts: 0,
    });
  }

  async changePassword(id: string, newPassword: string): Promise<void> {
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    await this.employeesRepository.update(id, {
      password: hashedPassword,
      passwordChangedAt: new Date(),
    });
  }

  async validatePassword(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}
