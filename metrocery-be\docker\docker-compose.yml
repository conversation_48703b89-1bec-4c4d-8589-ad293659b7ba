version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: metrocery-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-123123}
      MYSQL_DATABASE: ${DB_DATABASE:-metrocery}
      MYSQL_USER: ${DB_USERNAME:-metrocery}
      MYSQL_PASSWORD: ${DB_PASSWORD:-123123}
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/conf/my.cnf:/etc/mysql/conf.d/my.cnf
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - metrocery-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

volumes:
  mysql_data:
    driver: local

networks:
  metrocery-network:
    driver: bridge
