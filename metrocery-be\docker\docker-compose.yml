version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: metrocery-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123123
      MYSQL_DATABASE: metrocery
      MYSQL_USER: metrocery
      MYSQL_PASSWORD: 123123
    ports:
      - '3306:3306'
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password

volumes:
  mysql_data:
