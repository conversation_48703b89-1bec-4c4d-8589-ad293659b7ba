import { NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest) {
  const token = request.cookies.get("token")?.value;

  // Nếu chưa đăng nhập, chuyển hướng tất cả trang trừ /login về /login
  if (!token && request.nextUrl.pathname !== "/login") {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  // Nếu đã đăng nhập, vào /login thì chuyển hướng sang /dashboard
  if (token && request.nextUrl.pathname === "/login") {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Nếu đã đăng nhập, vào trang chủ thì chuyển hướng sang dashboard
  if (token && request.nextUrl.pathname === "/") {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Cho phép truy cập các trang khác
  return NextResponse.next();
}

export const config = {
  matcher: [
    "/",
    "/dashboard",
    "/dashboard/products",
    "/login",
    "/employees",
    "/employees/:path*",
    "/customers",
    "/customers/:path*",
    "/imports",
    "/imports/:path*",
  ],
};
