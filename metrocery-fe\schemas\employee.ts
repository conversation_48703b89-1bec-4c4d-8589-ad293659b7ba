import { z } from "zod";

export const employeeRoles = [
  { label: "Super Admin", value: "super_admin" },
  { label: "Admin", value: "admin" },
  { label: "Staff", value: "staff" },
] as const;

export type EmployeeRole = (typeof employeeRoles)[number]["value"];

export const employeeSchema = z.object({
  id: z.string().optional(),
  fullName: z.string().min(1, "Họ tên không được để trống"),
  email: z.string().min(1, "Email không được để trống").email("Email không hợp lệ"),
  phone: z
    .string()
    .min(1, "Số điện thoại không được để trống")
    .regex(/^[0-9]{10}$/, "Số điện thoại phải có 10 chữ số"),
  role: z.enum(["super_admin", "admin", "staff"], {
    required_error: "<PERSON><PERSON> tr<PERSON> không được để trống",
  }),
  avatar: z.string().optional(),
  joinDate: z.date({
    required_error: "<PERSON><PERSON><PERSON> tham gia không được để trống",
  }),
  status: z.enum(["active", "inactive"], {
    required_error: "Trạng thái không được để trống",
  }),
});

export type EmployeeFormValues = z.infer<typeof employeeSchema>;

export const employeeStatusOptions = [
  { label: "Đang hoạt động", value: "active" },
  { label: "Không hoạt động", value: "inactive" },
];
