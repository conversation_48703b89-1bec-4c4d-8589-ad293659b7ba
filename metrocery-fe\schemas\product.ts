import { z } from "zod";

export const productSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Tên sản phẩm không được để trống"),
  image: z.string(),
  batch: z.string().min(1, "<PERSON>ô sản phẩm không được để trống"),
  expiryDate: z.date({
    required_error: "<PERSON><PERSON>y hết hạn không được để trống",
  }),
  quantity: z.coerce.number().min(1, "Số lượng phải lớn hơn 0"),
  category: z.string().min(1, "Loại sản phẩm không được để trống"),
  importPrice: z.coerce.number().min(0, "Gi<PERSON> nhập không được âm").default(0),
  sellingPrice: z.coerce.number().min(0, "<PERSON><PERSON><PERSON> <PERSON><PERSON> không được âm").default(0),
  profitPercent: z.coerce
    .number()
    .min(0, "Phần trăm lợi nhuận không được âm")
    .default(0),
});

export type ProductFormValues = z.infer<typeof productSchema>;

export const productCategories = [
  { label: "Thực phẩm", value: "food" },
  { label: "Đồ uống", value: "beverage" },
  { label: "Đồ gia dụng", value: "household" },
  { label: "Đồ dùng cá nhân", value: "personal" },
  { label: "Đồ điện tử", value: "electronics" },
  { label: "Khác", value: "other" },
];
