import { z } from "zod";

// Schema cho sản phẩm trong phiếu nhập
export const importItemSchema = z.object({
  id: z.string().optional(),
  productId: z.string().min(1, "ID sản phẩm không được để trống"),
  productName: z.string().min(1, "Tên sản phẩm không được để trống"),
  quantity: z.coerce.number().min(1, "Số lượng phải lớn hơn 0"),
  importPrice: z.coerce.number().min(0, "<PERSON>i<PERSON> nhập không được âm"),
  batch: z.string().min(1, "<PERSON>ô sản phẩm không được để trống"),
  expiryDate: z.date().optional(),
  subtotal: z.coerce.number().min(0, "Thành tiền không được âm"),
});

export type ImportItemValues = z.infer<typeof importItemSchema>;

// Schema cho phiếu nhập hàng
export const importSchema = z.object({
  id: z.string().optional(),
  code: z.string().min(1, "Mã phiếu nhập không được để trống"),
  importDate: z.date({
    required_error: "Ngày nhập không được để trống",
  }),
  supplierId: z.string().min(1, "ID nhà cung cấp không được để trống"),
  supplierName: z.string().min(1, "Tên nhà cung cấp không được để trống"),
  employeeId: z.string().min(1, "ID nhân viên không được để trống"),
  employeeName: z.string().min(1, "Tên nhân viên không được để trống"),
  items: z.array(importItemSchema).min(1, "Phải có ít nhất một sản phẩm"),
  totalAmount: z.coerce.number().min(0, "Tổng giá trị không được âm"),
  status: z.enum(["completed", "processing", "cancelled"], {
    required_error: "Trạng thái không được để trống",
  }),
  note: z.string().optional(),
});

export type ImportFormValues = z.infer<typeof importSchema>;

export const importStatusOptions = [
  { label: "Đã nhập", value: "completed" },
  { label: "Đang xử lý", value: "processing" },
  { label: "Đã hủy", value: "cancelled" },
];

// Danh sách nhà cung cấp mẫu
export const supplierOptions = [
  { label: "Công ty TNHH Thực phẩm ABC", value: "supplier_1" },
  { label: "Công ty CP Thực phẩm XYZ", value: "supplier_2" },
  { label: "Công ty TNHH Đồ uống DEF", value: "supplier_3" },
  { label: "Công ty TNHH Hàng tiêu dùng GHI", value: "supplier_4" },
  { label: "Công ty CP Thực phẩm sạch JKL", value: "supplier_5" },
];
