# 🐳 Docker Setup cho Metrocery Backend

## <PERSON><PERSON><PERSON> cầu
- Docker Desktop đã được cài đặt và chạy
- Port 3306 và 8080 không bị sử dụng

## Cách sử dụng

### 1. Khởi động MySQL
```bash
docker-compose up -d
```

### 2. <PERSON><PERSON><PERSON> tra trạng thái
```bash
docker-compose ps
```

### 3. Xem logs
```bash
docker-compose logs -f mysql
```

### 4. Dừng MySQL
```bash
docker-compose down
```

## Thông tin kết nối

### MySQL Database
- **Host**: localhost
- **Port**: 3306
- **Database**: metrocery
- **Username**: metrocery
- **Password**: 123123

### phpMyAdmin (Web Interface)
- **URL**: http://localhost:8080
- **Username**: metrocery
- **Password**: 123123

## Troubleshooting

### Lỗi port đã được sử dụng
```bash
# Ki<PERSON><PERSON> tra process sử dụng port 3306
netstat -ano | findstr :3306

# Kill process nếu cần
taskkill /PID <PID_NUMBER> /F
```

### Reset database
```bash
# Xóa container và volume
docker-compose down -v

# Khởi động lại
docker-compose up -d
```

### Xem container đang chạy
```bash
docker ps
```
