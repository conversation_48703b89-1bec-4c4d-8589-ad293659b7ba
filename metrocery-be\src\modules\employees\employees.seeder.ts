import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Employee, EmployeeRole, EmployeeStatus } from './entities/employee.entity';

@Injectable()
export class EmployeesSeeder {
  private readonly logger = new Logger(EmployeesSeeder.name);

  constructor(
    @InjectRepository(Employee)
    private readonly employeesRepository: Repository<Employee>,
  ) {}

  async seedDefaultAdmin(): Promise<void> {
    try {
      // Kiểm tra xem đã có super admin chưa
      const existingSuperAdmin = await this.employeesRepository.findOne({
        where: { role: EmployeeRole.SUPER_ADMIN },
      });

      if (existingSuperAdmin) {
        this.logger.log('Super Admin already exists, skipping seed');
        return;
      }

      // Tạo super admin mặc định
      const defaultAdmin = this.employeesRepository.create({
        fullName: 'Super Administrator',
        email: '<EMAIL>',
        phone: '0123456789',
        avatar: null,
        role: EmployeeRole.SUPER_ADMIN,
        status: EmployeeStatus.ACTIVE,
        joinDate: new Date('2025-01-01'),
      });

      await this.employeesRepository.save(defaultAdmin);
      
      this.logger.log('✅ Default Super Admin created successfully');
      this.logger.log(`📧 Email: ${defaultAdmin.email}`);
      this.logger.log(`👤 Role: ${defaultAdmin.role}`);
      this.logger.log(`🆔 ID: ${defaultAdmin.id}`);

    } catch (error) {
      this.logger.error('❌ Failed to create default Super Admin', error);
      throw error;
    }
  }

  async seedSampleEmployees(): Promise<void> {
    try {
      const sampleEmployees = [
        {
          fullName: 'Nguyễn Văn Manager',
          email: '<EMAIL>',
          phone: '0123456790',
          role: EmployeeRole.ADMIN,
          status: EmployeeStatus.ACTIVE,
          joinDate: new Date('2025-01-15'),
        },
        {
          fullName: 'Trần Thị Staff',
          email: '<EMAIL>',
          phone: '0123456791',
          role: EmployeeRole.STAFF,
          status: EmployeeStatus.ACTIVE,
          joinDate: new Date('2025-01-20'),
        },
      ];

      for (const employeeData of sampleEmployees) {
        const existingEmployee = await this.employeesRepository.findOne({
          where: { email: employeeData.email },
        });

        if (!existingEmployee) {
          const employee = this.employeesRepository.create(employeeData);
          await this.employeesRepository.save(employee);
          this.logger.log(`✅ Created sample employee: ${employee.email}`);
        }
      }

    } catch (error) {
      this.logger.error('❌ Failed to create sample employees', error);
      throw error;
    }
  }
}
