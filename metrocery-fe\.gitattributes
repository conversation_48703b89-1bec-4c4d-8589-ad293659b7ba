# Auto detect text files and perform LF normalization
* text=auto

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.flv binary
*.fla binary
*.swf binary
*.gz binary
*.zip binary
*.7z binary
*.ttf binary
*.eot binary
*.woff binary
*.pyc binary
*.pdf binary

# Source code
*.ts text
*.tsx text
*.js text
*.jsx text
*.json text
*.css text
*.scss text
*.sass text
*.less text
*.html text
*.md text
*.yml text
*.yaml text
*.xml text
*.svg text
*.graphql text
*.gql text

# Lock files
package-lock.json text
yarn.lock text
pnpm-lock.yaml text

# Git files
.gitignore text
.gitattributes text

# Next.js specific
.next/ export-ignore
out/ export-ignore
dist/ export-ignore
build/ export-ignore

# Environment files
.env* text
.env.local* text
.env.development* text
.env.production* text
.env.test* text

# Editor directories and files
.idea/ export-ignore
.vscode/ export-ignore
*.suo text
*.ntvs* text
*.njsproj text
*.sln text
*.sw? text 