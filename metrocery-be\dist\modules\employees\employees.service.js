"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmployeesService = void 0;
const common_1 = require("@nestjs/common");
const bcrypt = require("bcrypt");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const employee_entity_1 = require("./entities/employee.entity");
let EmployeesService = class EmployeesService {
    employeesRepository;
    constructor(employeesRepository) {
        this.employeesRepository = employeesRepository;
    }
    async create(createEmployeeDto) {
        const existingEmployee = await this.employeesRepository.findOne({
            where: { email: createEmployeeDto.email },
        });
        if (existingEmployee) {
            throw new common_1.ConflictException('Email already exists');
        }
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(createEmployeeDto.password, saltRounds);
        const employee = this.employeesRepository.create({
            ...createEmployeeDto,
            password: hashedPassword,
            passwordChangedAt: new Date(),
            joinDate: new Date(createEmployeeDto.joinDate),
        });
        return this.employeesRepository.save(employee);
    }
    async findAll(queryDto) {
        const { page = 1, limit = 10, search } = queryDto;
        const skip = (page - 1) * limit;
        const queryBuilder = this.employeesRepository.createQueryBuilder('employee');
        if (search) {
            queryBuilder.where('employee.fullName LIKE :search OR employee.email LIKE :search OR employee.phone LIKE :search', { search: `%${search}%` });
        }
        queryBuilder.orderBy('employee.createdAt', 'DESC');
        queryBuilder.skip(skip).take(limit);
        const [employees, total] = await queryBuilder.getManyAndCount();
        return {
            data: employees,
            total,
            page,
            limit,
        };
    }
    async findOne(id) {
        const employee = await this.employeesRepository.findOne({ where: { id } });
        if (!employee) {
            throw new common_1.NotFoundException(`Employee with ID ${id} not found`);
        }
        return employee;
    }
    async update(id, updateEmployeeDto) {
        const employee = await this.findOne(id);
        if (updateEmployeeDto.email && updateEmployeeDto.email !== employee.email) {
            const existingEmployee = await this.employeesRepository.findOne({
                where: { email: updateEmployeeDto.email },
            });
            if (existingEmployee) {
                throw new common_1.ConflictException('Email already exists');
            }
        }
        if (updateEmployeeDto.fullName)
            employee.fullName = updateEmployeeDto.fullName;
        if (updateEmployeeDto.email)
            employee.email = updateEmployeeDto.email;
        if (updateEmployeeDto.phone)
            employee.phone = updateEmployeeDto.phone;
        if (updateEmployeeDto.avatar !== undefined)
            employee.avatar = updateEmployeeDto.avatar;
        if (updateEmployeeDto.role)
            employee.role = updateEmployeeDto.role;
        if (updateEmployeeDto.status)
            employee.status = updateEmployeeDto.status;
        if (updateEmployeeDto.joinDate) {
            employee.joinDate = new Date(updateEmployeeDto.joinDate);
        }
        return this.employeesRepository.save(employee);
    }
    async remove(id) {
        const employee = await this.findOne(id);
        await this.employeesRepository.remove(employee);
        return { success: true };
    }
    async findByEmail(email) {
        return this.employeesRepository.findOne({
            where: { email },
            select: [
                'id',
                'email',
                'password',
                'role',
                'status',
                'fullName',
                'loginAttempts',
                'lockedUntil',
                'lastLoginAt',
            ],
        });
    }
    async updateLoginAttempts(id, attempts) {
        await this.employeesRepository.update(id, { loginAttempts: attempts });
    }
    async updateLastLogin(id) {
        await this.employeesRepository.update(id, {
            lastLoginAt: new Date(),
            loginAttempts: 0,
        });
    }
    async lockAccount(id, lockUntil) {
        await this.employeesRepository.update(id, {
            lockedUntil: lockUntil,
            loginAttempts: 0,
        });
    }
    async unlockAccount(id) {
        await this.employeesRepository.update(id, {
            lockedUntil: undefined,
            loginAttempts: 0,
        });
    }
    async changePassword(id, newPassword) {
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
        await this.employeesRepository.update(id, {
            password: hashedPassword,
            passwordChangedAt: new Date(),
        });
    }
    async validatePassword(plainPassword, hashedPassword) {
        return bcrypt.compare(plainPassword, hashedPassword);
    }
};
exports.EmployeesService = EmployeesService;
exports.EmployeesService = EmployeesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(employee_entity_1.Employee)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], EmployeesService);
//# sourceMappingURL=employees.service.js.map