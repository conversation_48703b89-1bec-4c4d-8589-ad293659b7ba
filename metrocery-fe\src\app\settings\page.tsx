"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";

export default function SettingsPage() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div className="container mx-auto min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
      <div className="absolute inset-0 z-0">
        <div
          className="w-full h-full bg-cover bg-center opacity-80"
          style={{
            backgroundImage: "url('/images/coming-soon-bg.svg')",
          }}
        />
      </div>

      <div className="z-10 text-center px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isLoaded ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="mb-8"
        >
          <h1 className="text-5xl md:text-7xl font-bold mb-4 text-primary">
            Coming Soon
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-2xl mx-auto">
            Chúng tôi đang phát triển trang cài đặt để mang đến trải nghiệm tốt
            nhất cho bạn. Vui lòng quay lại sau!
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={isLoaded ? { opacity: 1 } : {}}
          transition={{ delay: 0.5, duration: 0.8 }}
          className="mt-8"
        >
          <div className="inline-block relative">
            <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary-foreground rounded-lg blur opacity-30 animate-pulse"></div>
            <button
              onClick={() => window.history.back()}
              className="relative bg-background hover:bg-muted px-6 py-3 rounded-lg font-medium transition-all duration-200"
            >
              Quay lại trang trước
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
