<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4F46E5" stop-opacity="0.2"/>
      <stop offset="100%" stop-color="#0EA5E9" stop-opacity="0.2"/>
    </linearGradient>
    <pattern id="pattern" width="40" height="40" patternUnits="userSpaceOnUse">
      <circle cx="20" cy="20" r="2" fill="#4F46E5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#bg)"/>
  <rect width="100%" height="100%" fill="url(#pattern)"/>
  <g transform="translate(600, 400)">
    <circle r="200" fill="none" stroke="#4F46E5" stroke-width="2" opacity="0.2">
      <animate attributeName="r" from="200" to="220" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" from="0.2" to="0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle r="180" fill="none" stroke="#4F46E5" stroke-width="2" opacity="0.3">
      <animate attributeName="r" from="180" to="200" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" from="0.3" to="0.1" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle r="160" fill="none" stroke="#4F46E5" stroke-width="2" opacity="0.4">
      <animate attributeName="r" from="160" to="180" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" from="0.4" to="0.2" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
