import { ApiProperty } from '@nestjs/swagger';
import { Employee } from '../entities/employee.entity';

export class EmployeeResponseDto {
  @ApiProperty({ description: 'Employee data' })
  data: Employee;
}

export class EmployeesListResponseDto {
  @ApiProperty({ description: 'List of employees', type: [Employee] })
  data: Employee[];

  @ApiProperty({ description: 'Total number of employees' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;
}

export class DeleteEmployeeResponseDto {
  @ApiProperty({ description: 'Success status' })
  success: boolean;
}

export class AdminCheckResponseDto {
  @ApiProperty({ description: 'Success status' })
  success: boolean;

  @ApiProperty({ description: 'Response message' })
  message: string;

  @ApiProperty({ description: 'Admin data', required: false })
  data?: {
    id: string;
    fullName: string;
    email: string;
    role: string;
    status: string;
    joinDate: Date;
    createdAt: Date;
  };

  @ApiProperty({ description: 'Additional note', required: false })
  note?: string;

  @ApiProperty({ description: 'Error message', required: false })
  error?: string;
}
