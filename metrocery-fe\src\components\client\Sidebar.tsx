"use client";
import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useSidebar } from "@/contexts/SidebarContext";

const menuItems = [
  { label: "Dashboard", icon: "🏠", href: "/dashboard" },
  { label: "Sản phẩm", icon: "📦", href: "/products" },
  { label: "Nhập hàng", icon: "📋", href: "/imports" },
  { label: "Nhân viên", icon: "👥", href: "/employees" },
  { label: "<PERSON>h<PERSON>ch hàng", icon: "👤", href: "/customers" },
  { label: "Settings", icon: "⚙️", href: "/settings" },
];

export default function Sidebar() {
  const pathname = usePathname();
  const { collapsed, toggleSidebar } = useSidebar();

  return (
    <div className="relative">
      {/* <PERSON>bar chính */}
      <aside
        className={`${
          collapsed ? "w-20" : "w-64"
        } bg-white flex flex-col fixed z-20 transition-all duration-300 ease-in-out
                   my-4 ml-4 rounded-xl shadow-xl overflow-hidden inset-y-0`}
      >
        <div className="flex flex-col h-[calc(100vh-2rem)]">
          <div className="border-b h-[75px]">
            <div
              className={`flex items-center ${
                collapsed ? "justify-center" : "justify-start"
              } px-6 py-6`}
            >
              {!collapsed && (
                <div className="flex items-center gap-2">
                  <Image src="/favicon.ico" alt="logo" width={28} height={28} />
                  <span className="text-xl font-bold text-green-700">
                    Metrocery
                  </span>
                </div>
              )}
              {collapsed && (
                <Image src="/favicon.ico" alt="logo" width={32} height={32} />
              )}
            </div>
          </div>

          <nav className="mt-6 flex flex-col gap-1 px-2 overflow-y-auto flex-1">
            {menuItems.map((item) => (
              <div key={item.label} className="flex flex-col">
                <Link
                  href={item.href}
                  className={`flex items-center ${
                    collapsed ? "justify-center" : "justify-start"
                  } gap-3 px-4 py-3 text-base font-medium rounded-xl transition-all duration-150
                               hover:bg-green-50 hover:text-green-700 ${
                                 pathname.startsWith(item.href) &&
                                 item.href !== "/logout"
                                   ? "bg-orange-500 text-white hover:bg-orange-600 hover:text-white"
                                   : "text-gray-700"
                               } ${
                    item.href === "/logout"
                      ? "mt-8 text-red-500 hover:bg-red-50"
                      : ""
                  }`}
                >
                  <span className="text-xl">{item.icon}</span>
                  {!collapsed && <span>{item.label}</span>}
                </Link>
              </div>
            ))}
          </nav>
        </div>
      </aside>

      {/* Nút mở rộng/thu nhỏ sidebar - đặt bên ngoài overflow-hidden */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
        <button
          onClick={toggleSidebar}
          style={{
            position: "absolute",
            top: "79px",
            left: collapsed ? "72px" : "248px",
            transform: "translateX(50%)",
            transition: "left 0.3s ease-in-out",
          }}
          className="w-6 h-6 rounded-full bg-white shadow-md flex items-center justify-center hover:bg-gray-100 transition-colors cursor-pointer pointer-events-auto z-30"
        >
          {collapsed ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
        </button>
      </div>
    </div>
  );
}
