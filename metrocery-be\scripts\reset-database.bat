@echo off
echo 🗑️ Resetting MySQL database...
echo.

echo 📦 Stopping Docker containers...
cd /d "%~dp0\..\docker"
docker-compose down -v

echo.
echo 🚀 Starting fresh MySQL with init scripts...
docker-compose up -d

echo.
echo ⏳ Waiting for MySQL to initialize...
timeout /t 15 /nobreak > nul

echo.
echo ✅ Database reset completed!
echo 👤 Super Admin account created:
echo    📧 Email: <EMAIL>
echo    🔑 Role: super_admin
echo    🆔 ID: ********-0000-0000-0000-************
echo.
echo 💡 You can now start the NestJS application
echo    npm run start:dev

cd /d "%~dp0\.."
pause
