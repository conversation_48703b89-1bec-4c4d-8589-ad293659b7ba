import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../app.module';
import { DatabaseSeeder } from './database.seeder';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const logger = new Logger('SeedCommand');
  
  try {
    logger.log('🚀 Initializing NestJS application for seeding...');
    
    const app = await NestFactory.createApplicationContext(AppModule);
    const seeder = app.get(DatabaseSeeder);
    
    await seeder.run();
    
    await app.close();
    logger.log('✅ Seeding completed and application closed');
    process.exit(0);
    
  } catch (error) {
    logger.error('❌ Seeding failed:', error);
    process.exit(1);
  }
}

bootstrap();
