"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import {
  Search,
  Eye,
  FileText,
  Plus,
  Pencil,
  Trash2,
  MoreHorizontal,
} from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/shared/ui/button";
import { Input } from "@/components/shared/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/shared/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shared/ui/select";
import { importStatusOptions } from "../../../schemas/import";
import { mockImports } from "@/lib/mock-data";

// Định nghĩa kiểu dữ liệu cho phiếu nhập
interface ImportItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  importPrice: number;
  batch: string;
  expiryDate?: Date;
  subtotal: number;
}

interface ImportData {
  id: string;
  code: string;
  importDate: Date;
  supplierId: string;
  supplierName: string;
  employeeId: string;
  employeeName: string;
  items: ImportItem[];
  totalAmount: number;
  status: string;
  note?: string;
}
import ImportDetailDialog from "@/components/client/imports/ImportDetailDialog";
import ImportForm from "@/components/client/imports/ImportForm";
import DeleteImportDialog from "@/components/client/imports/DeleteImportDialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/shared/ui/dropdown-menu";

export default function ImportsPage() {
  const [imports, setImports] = useState<ImportData[]>([]);
  const [filteredImports, setFilteredImports] = useState<ImportData[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedImport, setSelectedImport] = useState<ImportData | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  useEffect(() => {
    // Lấy dữ liệu lịch sử nhập hàng từ localStorage hoặc sử dụng mock data
    const storedImports = localStorage.getItem("imports");
    if (storedImports) {
      const parsedImports = JSON.parse(storedImports);
      // Chuyển đổi chuỗi ngày thành đối tượng Date
      const importsWithDates = parsedImports.map((importData: any) => ({
        ...importData,
        importDate: new Date(importData.importDate),
        items: importData.items.map((item: any) => ({
          ...item,
          expiryDate: item.expiryDate ? new Date(item.expiryDate) : undefined,
        })),
      }));
      setImports(importsWithDates);
      setFilteredImports(importsWithDates);
    } else {
      setImports(mockImports);
      setFilteredImports(mockImports);
      localStorage.setItem("imports", JSON.stringify(mockImports));
    }
  }, []);

  // Xử lý tìm kiếm và lọc
  useEffect(() => {
    let filtered = imports;

    // Lọc theo trạng thái
    if (statusFilter && statusFilter !== "all") {
      filtered = filtered.filter(
        (importData) => importData.status === statusFilter
      );
    }

    // Tìm kiếm theo mã phiếu nhập hoặc nhà cung cấp
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (importData) =>
          importData.code.toLowerCase().includes(query) ||
          importData.supplierName.toLowerCase().includes(query)
      );
    }

    setFilteredImports(filtered);
  }, [searchQuery, statusFilter, imports]);

  // Format số tiền thành định dạng tiền tệ Việt Nam
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  // Lấy label trạng thái
  const getStatusLabel = (status: string) => {
    switch (status) {
      case "completed":
        return "Đã nhập";
      case "processing":
        return "Đang xử lý";
      case "cancelled":
        return "Đã hủy";
      default:
        return status;
    }
  };

  // Lấy class cho trạng thái
  const getStatusClass = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Xử lý thêm phiếu nhập
  const handleAddImport = (importData: ImportData) => {
    const updatedImports = [...imports, importData];
    setImports(updatedImports);
    localStorage.setItem("imports", JSON.stringify(updatedImports));
    setIsAddDialogOpen(false);
    toast.success("Thêm phiếu nhập thành công");
  };

  // Xử lý sửa phiếu nhập
  const handleEditImport = (importData: ImportData) => {
    const updatedImports = imports.map((item) =>
      item.id === importData.id ? importData : item
    );
    setImports(updatedImports);
    localStorage.setItem("imports", JSON.stringify(updatedImports));
    setIsEditDialogOpen(false);
    toast.success("Cập nhật phiếu nhập thành công");
  };

  // Xử lý xóa phiếu nhập
  const handleDeleteImport = (id: string) => {
    const updatedImports = imports.filter((item) => item.id !== id);
    setImports(updatedImports);
    localStorage.setItem("imports", JSON.stringify(updatedImports));
    setIsDeleteDialogOpen(false);
    toast.success("Xóa phiếu nhập thành công");
  };

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Lịch sử nhập hàng</h1>
        <div className="flex gap-2">
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Thêm phiếu nhập
          </Button>
          <Button variant="outline">
            <FileText className="mr-2 h-4 w-4" /> Xuất báo cáo
          </Button>
        </div>
      </div>

      <div className="mb-6 flex items-center gap-4">
        <div className="relative w-full max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
          <Input
            placeholder="Tìm kiếm theo mã phiếu hoặc nhà cung cấp..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="w-48">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Lọc trạng thái" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả</SelectItem>
              {importStatusOptions.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>STT</TableHead>
              <TableHead>Mã phiếu</TableHead>
              <TableHead>Ngày nhập</TableHead>
              <TableHead>Nhà cung cấp</TableHead>
              <TableHead>Người nhập</TableHead>
              <TableHead>Tổng giá trị</TableHead>
              <TableHead>Trạng thái</TableHead>
              <TableHead className="text-right">Thao tác</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredImports.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-10">
                  {imports.length === 0
                    ? "Không có dữ liệu nhập hàng nào"
                    : "Không tìm thấy phiếu nhập hàng nào"}
                </TableCell>
              </TableRow>
            ) : (
              filteredImports.map((importData, index) => (
                <TableRow key={importData.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{importData.code}</TableCell>
                  <TableCell>
                    {format(importData.importDate, "dd/MM/yyyy", {
                      locale: vi,
                    })}
                  </TableCell>
                  <TableCell>{importData.supplierName}</TableCell>
                  <TableCell>{importData.employeeName}</TableCell>
                  <TableCell>
                    {formatCurrency(importData.totalAmount)}
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${getStatusClass(
                        importData.status
                      )}`}
                    >
                      {getStatusLabel(importData.status)}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Mở menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedImport(importData);
                            setIsDetailDialogOpen(true);
                          }}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          Xem chi tiết
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedImport(importData);
                            setIsEditDialogOpen(true);
                          }}
                        >
                          <Pencil className="mr-2 h-4 w-4" />
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedImport(importData);
                            setIsDeleteDialogOpen(true);
                          }}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Xóa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Dialog xem chi tiết phiếu nhập */}
      {selectedImport && (
        <ImportDetailDialog
          open={isDetailDialogOpen}
          onOpenChange={setIsDetailDialogOpen}
          importData={selectedImport}
        />
      )}

      {/* Dialog thêm phiếu nhập */}
      <ImportForm
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSubmit={handleAddImport}
        title="Thêm phiếu nhập mới"
      />

      {/* Dialog chỉnh sửa phiếu nhập */}
      {selectedImport && (
        <ImportForm
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onSubmit={handleEditImport}
          defaultValues={selectedImport}
          title="Chỉnh sửa phiếu nhập"
        />
      )}

      {/* Dialog xóa phiếu nhập */}
      {selectedImport && (
        <DeleteImportDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onConfirm={() => handleDeleteImport(selectedImport.id)}
          importCode={selectedImport.code}
        />
      )}
    </div>
  );
}
