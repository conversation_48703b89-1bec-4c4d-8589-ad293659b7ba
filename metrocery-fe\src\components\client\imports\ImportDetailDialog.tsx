"use client";

import { format } from "date-fns";
import { vi } from "date-fns/locale";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/shared/ui/dialog";
import { Button } from "@/components/shared/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/shared/ui/table";
// Định nghĩa kiểu dữ liệu cho phiếu nhập
interface ImportItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  importPrice: number;
  batch: string;
  expiryDate?: Date;
  subtotal: number;
}

interface ImportData {
  id: string;
  code: string;
  importDate: Date;
  supplierId: string;
  supplierName: string;
  employeeId: string;
  employeeName: string;
  items: ImportItem[];
  totalAmount: number;
  status: string;
  note?: string;
}

interface ImportDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  importData: ImportData;
}

export default function ImportDetailDialog({
  open,
  onOpenChange,
  importData,
}: ImportDetailDialogProps) {
  // Format số tiền thành định dạng tiền tệ Việt Nam
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  // Lấy label trạng thái
  const getStatusLabel = (status: string) => {
    switch (status) {
      case "completed":
        return "Đã nhập";
      case "processing":
        return "Đang xử lý";
      case "cancelled":
        return "Đã hủy";
      default:
        return status;
    }
  };

  // Lấy class cho trạng thái
  const getStatusClass = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>Chi tiết phiếu nhập #{importData.code}</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-4 py-4">
          <div>
            <p className="text-sm font-medium">Mã phiếu nhập:</p>
            <p className="text-sm">{importData.code}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Ngày nhập:</p>
            <p className="text-sm">
              {format(importData.importDate, "dd/MM/yyyy", { locale: vi })}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Nhà cung cấp:</p>
            <p className="text-sm">{importData.supplierName}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Người nhập:</p>
            <p className="text-sm">{importData.employeeName}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Trạng thái:</p>
            <p className="text-sm">
              <span
                className={`px-2 py-1 rounded-full text-xs ${getStatusClass(
                  importData.status
                )}`}
              >
                {getStatusLabel(importData.status)}
              </span>
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Tổng giá trị:</p>
            <p className="text-sm font-semibold text-green-600">
              {formatCurrency(importData.totalAmount)}
            </p>
          </div>
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>STT</TableHead>
                <TableHead>Sản phẩm</TableHead>
                <TableHead>Số lượng</TableHead>
                <TableHead>Đơn giá</TableHead>
                <TableHead>Lô</TableHead>
                <TableHead>Hạn sử dụng</TableHead>
                <TableHead className="text-right">Thành tiền</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {importData.items.map((item, index) => (
                <TableRow key={item.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{item.productName}</TableCell>
                  <TableCell>{item.quantity}</TableCell>
                  <TableCell>{formatCurrency(item.importPrice)}</TableCell>
                  <TableCell>{item.batch}</TableCell>
                  <TableCell>
                    {item.expiryDate
                      ? format(item.expiryDate, "dd/MM/yyyy", { locale: vi })
                      : "N/A"}
                  </TableCell>
                  <TableCell className="text-right">
                    {formatCurrency(item.subtotal)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {importData.note && (
          <div className="mt-4">
            <p className="text-sm font-medium">Ghi chú:</p>
            <p className="text-sm mt-1 p-2 bg-gray-50 rounded-md">
              {importData.note}
            </p>
          </div>
        )}

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Đóng</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
