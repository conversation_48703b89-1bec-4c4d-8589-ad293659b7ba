-- Khởi tạo database cho Metrocery
CREATE DATABASE IF NOT EXISTS metrocery;
USE metrocery;

-- Tạo user nếu chưa tồn tại
CREATE USER IF NOT EXISTS 'metrocery'@'%' IDENTIFIED BY '123123';
GRANT ALL PRIVILEGES ON metrocery.* TO 'metrocery'@'%';
FLUSH PRIVILEGES;

-- Tạo bảng employees nếu chưa tồn tại (TypeORM sẽ tạo, nhưng để đảm bảo)
CREATE TABLE IF NOT EXISTS employees (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    fullName VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20) NOT NULL,
    avatar VARCHAR(255) NULL,
    role ENUM('super_admin', 'admin', 'staff') NOT NULL DEFAULT 'staff',
    joinDate DATE NOT NULL,
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    password VARCHAR(255) NOT NULL,
    lastLoginAt DATETIME(6) NULL,
    passwordChangedAt DATETIME(6) NULL,
    loginAttempts INT NOT NULL DEFAULT 0,
    lockedUntil DATETIME(6) NULL,
    createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
);

-- Tạo tài khoản Super Admin mặc định
INSERT IGNORE INTO employees (
    id,
    fullName,
    email,
    phone,
    avatar,
    role,
    joinDate,
    status,
    password,
    passwordChangedAt,
    loginAttempts,
    createdAt,
    updatedAt
) VALUES (
    '********-0000-0000-0000-********0001',
    'Super Administrator',
    '<EMAIL>',
    '**********',
    NULL,
    'super_admin',
    '2025-01-01',
    'active',
    '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    NOW(),
    0,
    NOW(),
    NOW()
);

-- Log thông báo
SELECT 'Database metrocery đã được khởi tạo thành công!' as message;
SELECT 'Super Admin account created: <EMAIL>' as admin_info;
