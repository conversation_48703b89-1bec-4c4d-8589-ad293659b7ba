version: '3.8'

services:
  mysql:
    extends:
      file: docker-compose.yml
      service: mysql
    environment:
      MYSQL_ROOT_PASSWORD: 123123
      MYSQL_DATABASE: metrocery_dev
      MYSQL_USER: metrocery
      MYSQL_PASSWORD: 123123
    ports:
      - "3306:3306"

  # Chỉ có trong development - để debug database
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: metrocery-phpmyadmin-dev
    restart: always
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: metrocery
      PMA_PASSWORD: 123123
    ports:
      - "8080:80"
    depends_on:
      - mysql
    networks:
      - metrocery-network

  # Redis cho caching (optional)
  redis:
    image: redis:7-alpine
    container_name: metrocery-redis-dev
    restart: always
    ports:
      - "6379:6379"
    networks:
      - metrocery-network

networks:
  metrocery-network:
    driver: bridge
