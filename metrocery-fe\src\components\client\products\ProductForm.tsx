"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { Button } from "@/components/shared/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/shared/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/shared/ui/form";
import { Input } from "@/components/shared/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shared/ui/select";
import { DatePicker } from "@/components/shared/ui/date-picker";
import {
  productSchema,
  ProductFormValues,
  productCategories,
} from "../../../../schemas/product";
import { useEffect, useState } from "react";

interface ProductFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ProductFormValues) => void;
  defaultValues?: ProductFormValues;
  title: string;
}

export default function ProductForm({
  open,
  onOpenChange,
  onSubmit,
  defaultValues,
  title,
}: ProductFormProps) {
  const [isPriceEditing, setIsPriceEditing] = useState(false);
  const [isPercentEditing, setIsPercentEditing] = useState(false);

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: defaultValues || {
      name: "",
      image: "",
      batch: "",
      expiryDate: undefined,
      quantity: 1,
      category: "",
      importPrice: 0,
      sellingPrice: 0,
      profitPercent: 0,
    },
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // Xử lý khi thay đổi giá nhập
  const handleImportPriceChange = (value: number) => {
    const importPrice = value;
    const profitPercent = form.getValues("profitPercent");

    if (!isPriceEditing) {
      // Tính giá bán dựa trên % lợi nhuận
      const sellingPrice = Math.round(importPrice * (1 + profitPercent / 100));
      form.setValue("sellingPrice", sellingPrice);
    }
  };

  // Xử lý khi thay đổi giá bán
  const handleSellingPriceChange = (value: number) => {
    const sellingPrice = value;
    const importPrice = form.getValues("importPrice");

    if (importPrice > 0) {
      // Tính % lợi nhuận dựa trên giá bán và giá nhập
      const profitPercent = Math.round(
        ((sellingPrice - importPrice) / importPrice) * 100
      );
      form.setValue("profitPercent", profitPercent);
    }
  };

  // Xử lý khi thay đổi % lợi nhuận
  const handleProfitPercentChange = (value: number) => {
    const profitPercent = value;
    const importPrice = form.getValues("importPrice");

    if (!isPercentEditing && importPrice > 0) {
      // Tính giá bán dựa trên % lợi nhuận
      const sellingPrice = Math.round(importPrice * (1 + profitPercent / 100));
      form.setValue("sellingPrice", sellingPrice);
    }
  };

  const handleSubmit = (data: ProductFormValues) => {
    onSubmit({
      ...data,
      id: defaultValues?.id,
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên sản phẩm</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập tên sản phẩm" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="image"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>URL Ảnh</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập URL ảnh sản phẩm" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="batch"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Lô</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập lô sản phẩm" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="expiryDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Ngày hết hạn</FormLabel>
                  <DatePicker date={field.value} setDate={field.onChange} />
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Số lượng</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      placeholder="Nhập số lượng"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Loại sản phẩm</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn loại sản phẩm" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {productCategories.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="importPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Giá nhập (VNĐ)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={0}
                        {...field}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 0;
                          field.onChange(value);

                          // Tính giá bán dựa trên % lợi nhuận
                          const profitPercent = form.getValues("profitPercent");
                          const sellingPrice = Math.round(
                            value * (1 + profitPercent / 100)
                          );
                          form.setValue("sellingPrice", sellingPrice);
                        }}
                        onFocus={() => setIsPriceEditing(true)}
                        onBlur={() => setIsPriceEditing(false)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sellingPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Giá bán (VNĐ)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={0}
                        {...field}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 0;
                          field.onChange(value);

                          // Tính % lợi nhuận dựa trên giá bán và giá nhập
                          const importPrice = form.getValues("importPrice");
                          if (importPrice > 0) {
                            const profitPercent = Math.round(
                              ((value - importPrice) / importPrice) * 100
                            );
                            form.setValue("profitPercent", profitPercent);
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="profitPercent"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lợi nhuận (%)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={0}
                        {...field}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 0;
                          field.onChange(value);

                          // Tính giá bán dựa trên % lợi nhuận
                          const importPrice = form.getValues("importPrice");
                          if (importPrice > 0) {
                            const sellingPrice = Math.round(
                              importPrice * (1 + value / 100)
                            );
                            form.setValue("sellingPrice", sellingPrice);
                          }
                        }}
                        onFocus={() => setIsPercentEditing(true)}
                        onBlur={() => setIsPercentEditing(false)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Hủy
              </Button>
              <Button type="submit">Lưu</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
