export declare enum EmployeeRole {
    SUPER_ADMIN = "super_admin",
    ADMIN = "admin",
    STAFF = "staff"
}
export declare enum EmployeeStatus {
    ACTIVE = "active",
    INACTIVE = "inactive"
}
export declare class Employee {
    id: string;
    fullName: string;
    email: string;
    phone: string;
    avatar: string;
    role: EmployeeRole;
    joinDate: Date;
    status: EmployeeStatus;
    password: string;
    lastLoginAt: Date;
    passwordChangedAt: Date;
    loginAttempts: number;
    lockedUntil: Date;
    createdAt: Date;
    updatedAt: Date;
}
