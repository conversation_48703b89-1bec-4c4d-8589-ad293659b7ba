import { z } from "zod";

export const customerSchema = z.object({
  id: z.string().optional(),
  fullName: z.string().min(1, "H<PERSON> tên không được để trống"),
  email: z.string().min(1, "<PERSON>ail không được để trống").email("Email không hợp lệ"),
  phone: z
    .string()
    .min(1, "Số điện thoại không được để trống")
    .regex(/^[0-9]{10}$/, "Số điện thoại phải có 10 chữ số"),
  address: z.string().min(1, "Địa chỉ không được để trống"),
  avatar: z.string().optional(),
  joinDate: z.date({
    required_error: "Ngày tham gia không được để trống",
  }),
  status: z.enum(["active", "inactive"], {
    required_error: "Trạng thái không được để trống",
  }),
  totalOrders: z.number().min(0, "Số đơn hàng không được âm").default(0),
  totalSpent: z.number().min(0, "Tổng chi tiêu không được âm").default(0),
});

export type CustomerFormValues = z.infer<typeof customerSchema>;

export const customerStatusOptions = [
  { label: "Đang hoạt động", value: "active" },
  { label: "Không hoạt động", value: "inactive" },
];
