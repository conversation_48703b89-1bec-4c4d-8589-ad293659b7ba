version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: metrocery-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123123
      MYSQL_DATABASE: metrocery
      MYSQL_USER: metrocery
      MYSQL_PASSWORD: 123123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: metrocery-phpmyadmin
    restart: always
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: metrocery
      PMA_PASSWORD: 123123
    ports:
      - "8080:80"
    depends_on:
      - mysql

volumes:
  mysql_data:
