import { EmployeesService } from './employees.service';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { QueryEmployeeDto } from './dto/query-employee.dto';
export declare class EmployeesController {
    private readonly employeesService;
    constructor(employeesService: EmployeesService);
    create(createEmployeeDto: CreateEmployeeDto): Promise<{
        data: import("./entities/employee.entity").Employee;
    }>;
    findAll(queryDto: QueryEmployeeDto): Promise<{
        data: import("./entities/employee.entity").Employee[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: string): Promise<{
        data: import("./entities/employee.entity").Employee;
    }>;
    update(id: string, updateEmployeeDto: UpdateEmployeeDto): Promise<{
        data: import("./entities/employee.entity").Employee;
    }>;
    remove(id: string): Promise<{
        success: boolean;
    }>;
}
