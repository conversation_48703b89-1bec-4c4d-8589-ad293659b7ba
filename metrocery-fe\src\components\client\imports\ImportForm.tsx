"use client";

import { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { Plus, Trash2, X } from "lucide-react";

import { Button } from "@/components/shared/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/shared/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/shared/ui/form";
import { Input } from "@/components/shared/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shared/ui/select";
import { DatePicker } from "@/components/shared/ui/date-picker";
import { Textarea } from "@/components/shared/ui/textarea";
import { mockProducts } from "@/lib/mock-data";
import { mockEmployees } from "@/lib/mock-data";
import { supplierOptions, importStatusOptions } from "../../../../schemas/import";
import { z } from "zod";

// Định nghĩa schema cho form
const importItemSchema = z.object({
  id: z.string().optional(),
  productId: z.string().min(1, "Sản phẩm không được để trống"),
  productName: z.string().min(1, "Tên sản phẩm không được để trống"),
  quantity: z.coerce.number().min(1, "Số lượng phải lớn hơn 0"),
  importPrice: z.coerce.number().min(0, "Giá nhập không được âm"),
  batch: z.string().min(1, "Lô sản phẩm không được để trống"),
  expiryDate: z.date().optional(),
  subtotal: z.coerce.number().min(0, "Thành tiền không được âm"),
});

const importFormSchema = z.object({
  id: z.string().optional(),
  code: z.string().min(1, "Mã phiếu nhập không được để trống"),
  importDate: z.date({
    required_error: "Ngày nhập không được để trống",
  }),
  supplierId: z.string().min(1, "Nhà cung cấp không được để trống"),
  employeeId: z.string().min(1, "Nhân viên không được để trống"),
  items: z.array(importItemSchema).min(1, "Phải có ít nhất một sản phẩm"),
  status: z.enum(["completed", "processing", "cancelled"], {
    required_error: "Trạng thái không được để trống",
  }),
  note: z.string().optional(),
});

type ImportFormValues = z.infer<typeof importFormSchema>;
type ImportItemValues = z.infer<typeof importItemSchema>;

interface ImportFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ImportData) => void;
  defaultValues?: ImportData;
  title: string;
}

// Định nghĩa kiểu dữ liệu cho phiếu nhập
interface ImportItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  importPrice: number;
  batch: string;
  expiryDate?: Date;
  subtotal: number;
}

interface ImportData {
  id: string;
  code: string;
  importDate: Date;
  supplierId: string;
  supplierName: string;
  employeeId: string;
  employeeName: string;
  items: ImportItem[];
  totalAmount: number;
  status: string;
  note?: string;
}

export default function ImportForm({
  open,
  onOpenChange,
  onSubmit,
  defaultValues,
  title,
}: ImportFormProps) {
  const [products, setProducts] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);

  useEffect(() => {
    // Lấy danh sách sản phẩm
    setProducts(mockProducts);
    // Lấy danh sách nhân viên
    setEmployees(mockEmployees);
  }, []);

  // Khởi tạo form
  const form = useForm<ImportFormValues>({
    resolver: zodResolver(importFormSchema),
    defaultValues: defaultValues
      ? {
          id: defaultValues.id,
          code: defaultValues.code,
          importDate: defaultValues.importDate,
          supplierId: defaultValues.supplierId,
          employeeId: defaultValues.employeeId,
          items: defaultValues.items,
          status: defaultValues.status as "completed" | "processing" | "cancelled",
          note: defaultValues.note || "",
        }
      : {
          code: `PN${new Date().getTime().toString().slice(-6)}`,
          importDate: new Date(),
          supplierId: "",
          employeeId: "",
          items: [
            {
              id: new Date().getTime().toString(),
              productId: "",
              productName: "",
              quantity: 1,
              importPrice: 0,
              batch: "",
              subtotal: 0,
            },
          ],
          status: "processing",
          note: "",
        },
  });

  // Sử dụng useFieldArray để quản lý mảng items
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Xử lý khi chọn sản phẩm
  const handleProductChange = (productId: string, index: number) => {
    const product = products.find((p) => p.id === productId);
    if (product) {
      const currentItems = form.getValues("items");
      const currentItem = currentItems[index];
      const importPrice = currentItem.importPrice || 0;
      const quantity = currentItem.quantity || 1;
      const subtotal = importPrice * quantity;

      form.setValue(`items.${index}.productName`, product.name);
      form.setValue(`items.${index}.batch`, product.batch);
      form.setValue(`items.${index}.subtotal`, subtotal);
      if (product.expiryDate) {
        form.setValue(`items.${index}.expiryDate`, new Date(product.expiryDate));
      }
    }
  };

  // Xử lý khi thay đổi số lượng hoặc giá nhập
  const handleQuantityOrPriceChange = (index: number) => {
    const currentItems = form.getValues("items");
    const currentItem = currentItems[index];
    const importPrice = currentItem.importPrice || 0;
    const quantity = currentItem.quantity || 0;
    const subtotal = importPrice * quantity;

    form.setValue(`items.${index}.subtotal`, subtotal);
  };

  // Tính tổng giá trị
  const calculateTotal = (items: ImportItemValues[]) => {
    return items.reduce((total, item) => total + (item.subtotal || 0), 0);
  };

  // Xử lý khi submit form
  const handleSubmit = (data: ImportFormValues) => {
    // Tìm tên nhà cung cấp
    const supplier = supplierOptions.find((s) => s.value === data.supplierId);
    // Tìm tên nhân viên
    const employee = employees.find((e) => e.id === data.employeeId);

    // Tạo dữ liệu phiếu nhập
    const importData: ImportData = {
      id: data.id || new Date().getTime().toString(),
      code: data.code,
      importDate: data.importDate,
      supplierId: data.supplierId,
      supplierName: supplier?.label || "",
      employeeId: data.employeeId,
      employeeName: employee?.fullName || "",
      items: data.items.map((item) => ({
        ...item,
        id: item.id || new Date().getTime().toString(),
      })),
      totalAmount: calculateTotal(data.items),
      status: data.status,
      note: data.note,
    };

    onSubmit(importData);
  };

  // Format số tiền thành định dạng tiền tệ Việt Nam
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mã phiếu nhập</FormLabel>
                    <FormControl>
                      <Input {...field} readOnly={!!defaultValues} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="importDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Ngày nhập</FormLabel>
                    <DatePicker date={field.value} setDate={field.onChange} />
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="supplierId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nhà cung cấp</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn nhà cung cấp" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {supplierOptions.map((supplier) => (
                          <SelectItem key={supplier.value} value={supplier.value}>
                            {supplier.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nhân viên nhập</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn nhân viên" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {employees.map((employee) => (
                          <SelectItem key={employee.id} value={employee.id}>
                            {employee.fullName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Trạng thái</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn trạng thái" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {importStatusOptions.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <FormLabel>Ghi chú</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Nhập ghi chú (nếu có)"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Danh sách sản phẩm</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    append({
                      id: new Date().getTime().toString(),
                      productId: "",
                      productName: "",
                      quantity: 1,
                      importPrice: 0,
                      batch: "",
                      subtotal: 0,
                    })
                  }
                >
                  <Plus className="mr-2 h-4 w-4" /> Thêm sản phẩm
                </Button>
              </div>

              <div className="border rounded-md p-4 space-y-4">
                {fields.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">
                    Chưa có sản phẩm nào. Vui lòng thêm sản phẩm.
                  </div>
                ) : (
                  fields.map((field, index) => (
                    <div
                      key={field.id}
                      className="grid grid-cols-12 gap-4 items-end border-b pb-4"
                    >
                      <div className="col-span-3">
                        <FormField
                          control={form.control}
                          name={`items.${index}.productId`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={index !== 0 ? "sr-only" : ""}>
                                Sản phẩm
                              </FormLabel>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  handleProductChange(value, index);
                                }}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Chọn sản phẩm" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {products.map((product) => (
                                    <SelectItem key={product.id} value={product.id}>
                                      {product.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="col-span-2">
                        <FormField
                          control={form.control}
                          name={`items.${index}.quantity`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={index !== 0 ? "sr-only" : ""}>
                                Số lượng
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min={1}
                                  {...field}
                                  onChange={(e) => {
                                    field.onChange(parseInt(e.target.value) || 0);
                                    handleQuantityOrPriceChange(index);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="col-span-2">
                        <FormField
                          control={form.control}
                          name={`items.${index}.importPrice`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={index !== 0 ? "sr-only" : ""}>
                                Đơn giá
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min={0}
                                  {...field}
                                  onChange={(e) => {
                                    field.onChange(parseInt(e.target.value) || 0);
                                    handleQuantityOrPriceChange(index);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="col-span-2">
                        <FormField
                          control={form.control}
                          name={`items.${index}.batch`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={index !== 0 ? "sr-only" : ""}>
                                Lô
                              </FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="col-span-2">
                        <FormField
                          control={form.control}
                          name={`items.${index}.subtotal`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={index !== 0 ? "sr-only" : ""}>
                                Thành tiền
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  value={formatCurrency(field.value)}
                                  disabled
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="col-span-1 flex justify-end">
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => remove(index)}
                          disabled={fields.length === 1}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  ))
                )}

                <div className="flex justify-end pt-4">
                  <div className="text-lg font-semibold">
                    Tổng tiền:{" "}
                    {formatCurrency(
                      calculateTotal(form.getValues("items") || [])
                    )}
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Hủy
              </Button>
              <Button type="submit">Lưu</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
