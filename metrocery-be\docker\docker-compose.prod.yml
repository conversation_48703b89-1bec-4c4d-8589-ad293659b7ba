version: '3.8'

services:
  mysql:
    extends:
      file: docker-compose.yml
      service: mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    ports:
      - "127.0.0.1:3306:3306"  # Chỉ bind localhost
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/conf/my.cnf:/etc/mysql/conf.d/my.cnf
      # Backup volume
      - ./backups:/backups
    
  # Backup service
  mysql-backup:
    image: mysql:8.0
    container_name: metrocery-mysql-backup
    depends_on:
      - mysql
    environment:
      MYSQL_HOST: mysql
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
    volumes:
      - ./backups:/backups
      - ./scripts:/scripts
    command: >
      sh -c "
        while ! mysqladmin ping -h mysql --silent; do
          echo 'Waiting for MySQL...'
          sleep 5
        done
        echo 'MySQL is ready!'
        # Run backup script
        /scripts/backup.sh
      "
    networks:
      - metrocery-network

volumes:
  mysql_data:
    driver: local

networks:
  metrocery-network:
    driver: bridge
