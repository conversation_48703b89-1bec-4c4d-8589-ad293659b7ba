"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteEmployeeResponseDto = exports.EmployeesListResponseDto = exports.EmployeeResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const employee_entity_1 = require("../entities/employee.entity");
class EmployeeResponseDto {
    data;
}
exports.EmployeeResponseDto = EmployeeResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Employee data' }),
    __metadata("design:type", employee_entity_1.Employee)
], EmployeeResponseDto.prototype, "data", void 0);
class EmployeesListResponseDto {
    data;
    total;
    page;
    limit;
}
exports.EmployeesListResponseDto = EmployeesListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'List of employees', type: [employee_entity_1.Employee] }),
    __metadata("design:type", Array)
], EmployeesListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of employees' }),
    __metadata("design:type", Number)
], EmployeesListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Current page number' }),
    __metadata("design:type", Number)
], EmployeesListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of items per page' }),
    __metadata("design:type", Number)
], EmployeesListResponseDto.prototype, "limit", void 0);
class DeleteEmployeeResponseDto {
    success;
}
exports.DeleteEmployeeResponseDto = DeleteEmployeeResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Success status' }),
    __metadata("design:type", Boolean)
], DeleteEmployeeResponseDto.prototype, "success", void 0);
//# sourceMappingURL=employee-response.dto.js.map