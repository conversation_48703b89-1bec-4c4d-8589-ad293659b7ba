import { Injectable, Logger } from '@nestjs/common';
import { EmployeesSeeder } from '../../modules/employees/employees.seeder';

@Injectable()
export class DatabaseSeeder {
  private readonly logger = new Logger(DatabaseSeeder.name);

  constructor(
    private readonly employeesSeeder: EmployeesSeeder,
  ) {}

  async run(): Promise<void> {
    this.logger.log('🌱 Starting database seeding...');

    try {
      // Seed default admin
      await this.employeesSeeder.seedDefaultAdmin();
      
      // Seed sample employees (optional)
      await this.employeesSeeder.seedSampleEmployees();

      this.logger.log('🎉 Database seeding completed successfully!');
    } catch (error) {
      this.logger.error('💥 Database seeding failed:', error);
      throw error;
    }
  }
}
