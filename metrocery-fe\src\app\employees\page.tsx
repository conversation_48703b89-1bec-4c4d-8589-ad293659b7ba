"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { Plus, Pencil, Trash2, MoreHorizontal, Search } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/shared/ui/button";
import { Input } from "@/components/shared/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/shared/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/shared/ui/dropdown-menu";
import { EmployeeFormValues } from "../../../schemas/employee";
import { mockEmployees } from "@/lib/mock-data";
import EmployeeForm from "@/components/client/employees/EmployeeForm";
import DeleteEmployeeDialog from "@/components/client/employees/DeleteEmployeeDialog";

export default function EmployeesPage() {
  const [employees, setEmployees] = useState<EmployeeFormValues[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<
    EmployeeFormValues[]
  >([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] =
    useState<EmployeeFormValues | null>(null);
  const [currentUserRole, setCurrentUserRole] = useState<string>("super_admin"); // Mặc định là super_admin

  useEffect(() => {
    // Lấy dữ liệu nhân viên từ localStorage hoặc sử dụng mock data
    const storedEmployees = localStorage.getItem("employees");
    if (storedEmployees) {
      const parsedEmployees = JSON.parse(storedEmployees);
      // Chuyển đổi chuỗi ngày thành đối tượng Date
      const employeesWithDates = parsedEmployees.map(
        (employee: EmployeeFormValues) => ({
          ...employee,
          joinDate: new Date(employee.joinDate),
        })
      );
      setEmployees(employeesWithDates);
      setFilteredEmployees(employeesWithDates);
    } else {
      setEmployees(mockEmployees);
      setFilteredEmployees(mockEmployees);
      localStorage.setItem("employees", JSON.stringify(mockEmployees));
    }

    // Lấy thông tin người dùng hiện tại (giả lập)
    // Trong thực tế, bạn sẽ lấy thông tin này từ context hoặc API
    const userEmail = localStorage.getItem("userEmail") || "<EMAIL>";
    const userRole =
      employees.find((emp) => emp.email === userEmail)?.role || "super_admin";
    setCurrentUserRole(userRole);
  }, []);

  // Xử lý tìm kiếm nhân viên
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredEmployees(employees);
    } else {
      const filtered = employees.filter((employee) =>
        employee.fullName.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredEmployees(filtered);
    }
  }, [searchQuery, employees]);

  const handleAddEmployee = (employee: EmployeeFormValues) => {
    const newEmployee = {
      ...employee,
      id: Date.now().toString(),
    };
    const updatedEmployees = [...employees, newEmployee];
    setEmployees(updatedEmployees);
    localStorage.setItem("employees", JSON.stringify(updatedEmployees));
    setIsAddDialogOpen(false);
    toast.success("Thêm nhân viên thành công");
  };

  const handleEditEmployee = (employee: EmployeeFormValues) => {
    if (!employee.id) return;
    const updatedEmployees = employees.map((emp) =>
      emp.id === employee.id ? employee : emp
    );
    setEmployees(updatedEmployees);
    localStorage.setItem("employees", JSON.stringify(updatedEmployees));
    setIsEditDialogOpen(false);
    toast.success("Cập nhật nhân viên thành công");
  };

  const handleDeleteEmployee = (id: string) => {
    const updatedEmployees = employees.filter((employee) => employee.id !== id);
    setEmployees(updatedEmployees);
    localStorage.setItem("employees", JSON.stringify(updatedEmployees));
    setIsDeleteDialogOpen(false);
    toast.success("Xóa nhân viên thành công");
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case "super_admin":
        return "Super Admin";
      case "admin":
        return "Admin";
      case "staff":
        return "Nhân viên";
      default:
        return role;
    }
  };

  const getStatusLabel = (status: string) => {
    return status === "active" ? "Đang hoạt động" : "Không hoạt động";
  };

  // Kiểm tra quyền để thực hiện các hành động
  const canEdit = (employeeRole: string): boolean => {
    if (currentUserRole === "super_admin") return true;
    if (currentUserRole === "admin" && employeeRole !== "super_admin")
      return true;
    return false;
  };

  const canDelete = (employeeRole: string): boolean => {
    if (currentUserRole === "super_admin") return true;
    if (
      currentUserRole === "admin" &&
      employeeRole !== "super_admin" &&
      employeeRole !== "admin"
    )
      return true;
    return false;
  };

  const canAdd = (): boolean => {
    return currentUserRole === "super_admin" || currentUserRole === "admin";
  };

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Quản lý nhân viên</h1>
        {canAdd() && (
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Thêm nhân viên
          </Button>
        )}
      </div>

      <div className="mb-6 flex items-center">
        <div className="relative w-full max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
          <Input
            placeholder="Tìm kiếm nhân viên theo tên..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>STT</TableHead>
              <TableHead>Avatar</TableHead>
              <TableHead>Họ tên</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Số điện thoại</TableHead>
              <TableHead>Vai trò</TableHead>
              <TableHead>Ngày tham gia</TableHead>
              <TableHead>Trạng thái</TableHead>
              <TableHead className="text-right">Thao tác</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredEmployees.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-10">
                  {employees.length === 0
                    ? "Không có nhân viên nào"
                    : "Không tìm thấy nhân viên nào"}
                </TableCell>
              </TableRow>
            ) : (
              filteredEmployees.map((employee, index) => (
                <TableRow key={employee.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>
                    <img
                      src={employee.avatar}
                      alt={employee.fullName}
                      className="w-10 h-10 object-cover rounded-full"
                    />
                  </TableCell>
                  <TableCell>{employee.fullName}</TableCell>
                  <TableCell>{employee.email}</TableCell>
                  <TableCell>{employee.phone}</TableCell>
                  <TableCell>{getRoleLabel(employee.role)}</TableCell>
                  <TableCell>
                    {format(employee.joinDate, "dd/MM/yyyy", { locale: vi })}
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        employee.status === "active"
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {getStatusLabel(employee.status)}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Mở menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {canEdit(employee.role) && (
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedEmployee(employee);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Pencil className="mr-2 h-4 w-4" />
                            Chỉnh sửa
                          </DropdownMenuItem>
                        )}
                        {canDelete(employee.role) && (
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedEmployee(employee);
                              setIsDeleteDialogOpen(true);
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Xóa
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Dialog thêm nhân viên */}
      <EmployeeForm
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSubmit={handleAddEmployee}
        title="Thêm nhân viên mới"
        currentUserRole={currentUserRole}
      />

      {/* Dialog chỉnh sửa nhân viên */}
      {selectedEmployee && (
        <EmployeeForm
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onSubmit={handleEditEmployee}
          defaultValues={selectedEmployee}
          title="Chỉnh sửa nhân viên"
          currentUserRole={currentUserRole}
        />
      )}

      {/* Dialog xóa nhân viên */}
      {selectedEmployee && (
        <DeleteEmployeeDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onConfirm={() => handleDeleteEmployee(selectedEmployee.id!)}
          employeeName={selectedEmployee.fullName}
        />
      )}
    </div>
  );
}
