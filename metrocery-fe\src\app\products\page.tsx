"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { Plus, Pencil, Trash2, MoreHorizontal, Search, X } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/shared/ui/button";
import { Input } from "@/components/shared/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shared/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/shared/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/shared/ui/dropdown-menu";
import { ProductFormValues, productCategories } from "../../../schemas/product";
import { mockProducts } from "@/lib/mock-data";
import ProductForm from "@/components/client/products/ProductForm";
import DeleteProductDialog from "@/components/client/products/DeleteProductDialog";

export default function ProductsPage() {
  const [products, setProducts] = useState<ProductFormValues[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<ProductFormValues[]>(
    []
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] =
    useState<ProductFormValues | null>(null);

  useEffect(() => {
    const storedProducts = localStorage.getItem("products");
    if (storedProducts) {
      const parsedProducts = JSON.parse(storedProducts);
      // Chuyển đổi chuỗi ngày thành đối tượng Date
      const productsWithDates = parsedProducts.map(
        (product: ProductFormValues) => ({
          ...product,
          expiryDate: new Date(product.expiryDate),
        })
      );
      setProducts(productsWithDates);
      setFilteredProducts(productsWithDates);
    } else {
      setProducts(mockProducts);
      setFilteredProducts(mockProducts);
      localStorage.setItem("products", JSON.stringify(mockProducts));
    }
  }, []);

  // Xử lý tìm kiếm và lọc sản phẩm
  useEffect(() => {
    let filtered = products;

    // Lọc theo loại sản phẩm
    if (categoryFilter !== "all") {
      filtered = filtered.filter(
        (product) => product.category === categoryFilter
      );
    }

    // Tìm kiếm theo tên sản phẩm
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter((product) =>
        product.name.toLowerCase().includes(query)
      );
    }

    setFilteredProducts(filtered);
  }, [searchQuery, categoryFilter, products]);

  const handleAddProduct = (product: ProductFormValues) => {
    const newProduct = {
      ...product,
      id: Date.now().toString(),
    };
    const updatedProducts = [...products, newProduct];
    setProducts(updatedProducts);
    localStorage.setItem("products", JSON.stringify(updatedProducts));
    setIsAddDialogOpen(false);
    toast.success("Thêm sản phẩm thành công");
  };

  const handleEditProduct = (product: ProductFormValues) => {
    if (!product.id) return;
    const updatedProducts = products.map((p) =>
      p.id === product.id ? product : p
    );
    setProducts(updatedProducts);
    localStorage.setItem("products", JSON.stringify(updatedProducts));
    setIsEditDialogOpen(false);
    toast.success("Cập nhật sản phẩm thành công");
  };

  const handleDeleteProduct = (id: string) => {
    const updatedProducts = products.filter((product) => product.id !== id);
    setProducts(updatedProducts);
    localStorage.setItem("products", JSON.stringify(updatedProducts));
    setIsDeleteDialogOpen(false);
    toast.success("Xóa sản phẩm thành công");
  };

  // Xử lý xóa bộ lọc
  const handleClearFilters = () => {
    setSearchQuery("");
    setCategoryFilter("all");
  };

  // Format số tiền thành định dạng tiền tệ Việt Nam
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  const getCategoryLabel = (value: string) => {
    switch (value) {
      case "food":
        return "Thực phẩm";
      case "beverage":
        return "Đồ uống";
      case "household":
        return "Đồ gia dụng";
      case "personal":
        return "Đồ dùng cá nhân";
      case "electronics":
        return "Đồ điện tử";
      case "other":
        return "Khác";
      default:
        return value;
    }
  };

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Quản lý sản phẩm</h1>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Thêm sản phẩm
        </Button>
      </div>

      <div className="mb-6 flex items-center gap-4">
        <div className="relative w-full max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
          <Input
            placeholder="Tìm kiếm theo tên sản phẩm..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="w-48">
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Lọc loại sản phẩm" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả</SelectItem>
              {productCategories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {(searchQuery || categoryFilter !== "all") && (
          <Button variant="outline" size="sm" onClick={handleClearFilters}>
            <X className="mr-2 h-4 w-4" /> Xóa bộ lọc
          </Button>
        )}
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>STT</TableHead>
              <TableHead>Ảnh</TableHead>
              <TableHead>Tên sản phẩm</TableHead>
              <TableHead>Lô</TableHead>
              <TableHead>Ngày hết hạn</TableHead>
              <TableHead>Số lượng</TableHead>
              <TableHead>Loại</TableHead>
              <TableHead>Giá nhập</TableHead>
              <TableHead>Giá bán</TableHead>
              <TableHead>Lợi nhuận</TableHead>
              <TableHead className="text-right">Thao tác</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredProducts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-10">
                  {products.length === 0
                    ? "Không có sản phẩm nào"
                    : "Không tìm thấy sản phẩm phù hợp"}
                </TableCell>
              </TableRow>
            ) : (
              filteredProducts.map((product, index) => (
                <TableRow key={product.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-12 h-12 object-cover rounded-md"
                    />
                  </TableCell>
                  <TableCell className="font-medium">{product.name}</TableCell>
                  <TableCell>{product.batch}</TableCell>
                  <TableCell>
                    {format(new Date(product.expiryDate), "dd/MM/yyyy", {
                      locale: vi,
                    })}
                  </TableCell>
                  <TableCell>{product.quantity}</TableCell>
                  <TableCell>{getCategoryLabel(product.category)}</TableCell>
                  <TableCell>{formatCurrency(product.importPrice)}</TableCell>
                  <TableCell>{formatCurrency(product.sellingPrice)}</TableCell>
                  <TableCell>{product.profitPercent}%</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Mở menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            console.log(product);
                            setSelectedProduct(product);
                            setIsEditDialogOpen(true);
                          }}
                        >
                          <Pencil className="mr-2 h-4 w-4" />
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedProduct(product);
                            setIsDeleteDialogOpen(true);
                          }}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Xóa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Dialog thêm sản phẩm */}
      <ProductForm
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSubmit={handleAddProduct}
        title="Thêm sản phẩm mới"
      />

      {/* Dialog chỉnh sửa sản phẩm */}
      {selectedProduct && (
        <ProductForm
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onSubmit={handleEditProduct}
          defaultValues={selectedProduct}
          title="Chỉnh sửa sản phẩm"
        />
      )}

      {/* Dialog xóa sản phẩm */}
      {selectedProduct && (
        <DeleteProductDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onConfirm={() => handleDeleteProduct(selectedProduct.id!)}
          productName={selectedProduct.name}
        />
      )}
    </div>
  );
}
