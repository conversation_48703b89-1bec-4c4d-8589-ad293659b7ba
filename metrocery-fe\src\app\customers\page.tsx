"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { Plus, Pencil, Trash2, MoreHorizontal, Search } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/shared/ui/button";
import { Input } from "@/components/shared/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/shared/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/shared/ui/dropdown-menu";
import { CustomerFormValues } from "../../../schemas/customer";
import { mockCustomers } from "@/lib/mock-data";
import CustomerForm from "@/components/client/customers/CustomerForm";
import DeleteCustomerDialog from "@/components/client/customers/DeleteCustomerDialog";

export default function CustomersPage() {
  const [customers, setCustomers] = useState<CustomerFormValues[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<CustomerFormValues[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerFormValues | null>(null);

  useEffect(() => {
    // Lấy dữ liệu khách hàng từ localStorage hoặc sử dụng mock data
    const storedCustomers = localStorage.getItem("customers");
    if (storedCustomers) {
      const parsedCustomers = JSON.parse(storedCustomers);
      // Chuyển đổi chuỗi ngày thành đối tượng Date
      const customersWithDates = parsedCustomers.map(
        (customer: CustomerFormValues) => ({
          ...customer,
          joinDate: new Date(customer.joinDate),
        })
      );
      setCustomers(customersWithDates);
      setFilteredCustomers(customersWithDates);
    } else {
      setCustomers(mockCustomers);
      setFilteredCustomers(mockCustomers);
      localStorage.setItem("customers", JSON.stringify(mockCustomers));
    }
  }, []);

  // Xử lý tìm kiếm khách hàng
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredCustomers(customers);
    } else {
      const filtered = customers.filter((customer) =>
        customer.fullName.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCustomers(filtered);
    }
  }, [searchQuery, customers]);

  const handleAddCustomer = (customer: CustomerFormValues) => {
    const newCustomer = {
      ...customer,
      id: Date.now().toString(),
    };
    const updatedCustomers = [...customers, newCustomer];
    setCustomers(updatedCustomers);
    localStorage.setItem("customers", JSON.stringify(updatedCustomers));
    setIsAddDialogOpen(false);
    toast.success("Thêm khách hàng thành công");
  };

  const handleEditCustomer = (customer: CustomerFormValues) => {
    if (!customer.id) return;
    const updatedCustomers = customers.map((cust) =>
      cust.id === customer.id ? customer : cust
    );
    setCustomers(updatedCustomers);
    localStorage.setItem("customers", JSON.stringify(updatedCustomers));
    setIsEditDialogOpen(false);
    toast.success("Cập nhật khách hàng thành công");
  };

  const handleDeleteCustomer = (id: string) => {
    const updatedCustomers = customers.filter((customer) => customer.id !== id);
    setCustomers(updatedCustomers);
    localStorage.setItem("customers", JSON.stringify(updatedCustomers));
    setIsDeleteDialogOpen(false);
    toast.success("Xóa khách hàng thành công");
  };

  const getStatusLabel = (status: string) => {
    return status === "active" ? "Đang hoạt động" : "Không hoạt động";
  };

  // Format số tiền thành định dạng tiền tệ Việt Nam
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Quản lý khách hàng</h1>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Thêm khách hàng
        </Button>
      </div>

      <div className="mb-6 flex items-center">
        <div className="relative w-full max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
          <Input
            placeholder="Tìm kiếm khách hàng theo tên..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>STT</TableHead>
              <TableHead>Avatar</TableHead>
              <TableHead>Họ tên</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Số điện thoại</TableHead>
              <TableHead>Địa chỉ</TableHead>
              <TableHead>Ngày tham gia</TableHead>
              <TableHead>Số đơn hàng</TableHead>
              <TableHead>Tổng chi tiêu</TableHead>
              <TableHead>Trạng thái</TableHead>
              <TableHead className="text-right">Thao tác</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCustomers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={11} className="text-center py-10">
                  {customers.length === 0
                    ? "Không có khách hàng nào"
                    : "Không tìm thấy khách hàng nào"}
                </TableCell>
              </TableRow>
            ) : (
              filteredCustomers.map((customer, index) => (
                <TableRow key={customer.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>
                    <img
                      src={customer.avatar}
                      alt={customer.fullName}
                      className="w-10 h-10 object-cover rounded-full"
                    />
                  </TableCell>
                  <TableCell>{customer.fullName}</TableCell>
                  <TableCell>{customer.email}</TableCell>
                  <TableCell>{customer.phone}</TableCell>
                  <TableCell className="max-w-[200px] truncate" title={customer.address}>
                    {customer.address}
                  </TableCell>
                  <TableCell>
                    {format(customer.joinDate, "dd/MM/yyyy", { locale: vi })}
                  </TableCell>
                  <TableCell>{customer.totalOrders}</TableCell>
                  <TableCell>{formatCurrency(customer.totalSpent)}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        customer.status === "active"
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {getStatusLabel(customer.status)}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Mở menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedCustomer(customer);
                            setIsEditDialogOpen(true);
                          }}
                        >
                          <Pencil className="mr-2 h-4 w-4" />
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedCustomer(customer);
                            setIsDeleteDialogOpen(true);
                          }}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Xóa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Dialog thêm khách hàng */}
      <CustomerForm
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSubmit={handleAddCustomer}
        title="Thêm khách hàng mới"
      />

      {/* Dialog chỉnh sửa khách hàng */}
      {selectedCustomer && (
        <CustomerForm
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onSubmit={handleEditCustomer}
          defaultValues={selectedCustomer}
          title="Chỉnh sửa khách hàng"
        />
      )}

      {/* Dialog xóa khách hàng */}
      {selectedCustomer && (
        <DeleteCustomerDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onConfirm={() => handleDeleteCustomer(selectedCustomer.id!)}
          customerName={selectedCustomer.fullName}
        />
      )}
    </div>
  );
}
