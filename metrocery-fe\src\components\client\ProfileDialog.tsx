"use client";

import { useState, useEffect } from "react";
import { FiUser, FiMail, FiPhone, FiCalendar, FiEdit } from "react-icons/fi";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/shared/ui/dialog";
import { <PERSON><PERSON> } from "@/components/shared/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/shared/ui/avatar";
import { Separator } from "@/components/shared/ui/separator";
import { toast } from "sonner";

interface ProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function ProfileDialog({ open, onOpenChange }: ProfileDialogProps) {
  const [userProfile, setUserProfile] = useState({
    name: "Admin",
    email: "<EMAIL>",
    role: "Super Admin",
    phone: "0123456789",
    joinDate: "01/01/2023",
  });

  useEffect(() => {
    // <PERSON><PERSON>y thông tin người dùng từ localStorage
    const storedEmail = localStorage.getItem("userEmail");
    if (storedEmail) {
      // Giả lập lấy tên người dùng từ email
      const name = storedEmail.split("@")[0];
      const formattedName = name.charAt(0).toUpperCase() + name.slice(1);
      
      setUserProfile({
        ...userProfile,
        name: formattedName,
        email: storedEmail,
      });
    }
  }, [open]);

  const handleEditProfile = () => {
    toast.success("Chức năng chỉnh sửa hồ sơ sẽ được cập nhật trong tương lai!");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Hồ sơ cá nhân</DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col items-center py-4">
          <Avatar className="h-24 w-24 mb-4">
            <AvatarImage src="/favicon.ico" alt={userProfile.name} />
            <AvatarFallback>{userProfile.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <h2 className="text-xl font-bold">{userProfile.name}</h2>
          <p className="text-sm text-muted-foreground">{userProfile.role}</p>
        </div>
        
        <Separator />
        
        <div className="space-y-4 py-4">
          <div className="flex items-center gap-3">
            <FiMail className="text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Email</p>
              <p className="text-sm text-muted-foreground">{userProfile.email}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <FiPhone className="text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Số điện thoại</p>
              <p className="text-sm text-muted-foreground">{userProfile.phone}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <FiCalendar className="text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Ngày tham gia</p>
              <p className="text-sm text-muted-foreground">{userProfile.joinDate}</p>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button onClick={handleEditProfile} className="w-full">
            <FiEdit className="mr-2 h-4 w-4" />
            Chỉnh sửa hồ sơ
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
