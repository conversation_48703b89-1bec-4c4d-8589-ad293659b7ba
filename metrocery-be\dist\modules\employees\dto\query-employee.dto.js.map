{"version": 3, "file": "query-employee.dto.js", "sourceRoot": "", "sources": ["../../../../src/modules/employees/dto/query-employee.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA4D;AAC5D,6CAAsD;AACtD,yDAAyC;AAEzC,MAAa,gBAAgB;IAK3B,IAAI,GAAY,CAAC,CAAC;IAUlB,KAAK,GAAY,EAAE,CAAC;IAOpB,MAAM,CAAU;CACjB;AAvBD,4CAuBC;AAlBC;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC3E,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,qBAAG,EAAC,CAAC,CAAC;;8CACW;AAUlB;IARC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,0BAA0B;QACvC,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,qBAAG,EAAC,CAAC,CAAC;;+CACa;AAOpB;IALC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,2CAA2C;KACzD,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gDACK"}