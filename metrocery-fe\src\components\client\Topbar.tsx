"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, FiUser, FiLogOut } from "react-icons/fi";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/shared/ui/dropdown-menu";
import ProfileDialog from "./ProfileDialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../shared/ui/tooltip";
import { useSidebar } from "@/contexts/SidebarContext";

export default function Topbar() {
  const router = useRouter();
  const [userName, setUserName] = useState("Admin");
  const [userEmail, setUserEmail] = useState("<EMAIL>");
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const { collapsed: isSidebarCollapsed } = useSidebar();

  const today = new Date();
  const dateString = today.toLocaleDateString("vi-VN", {
    weekday: "long",
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });

  useEffect(() => {
    // Lấy thông tin người dùng từ localStorage
    const storedEmail = localStorage.getItem("userEmail");
    if (storedEmail) {
      setUserEmail(storedEmail);
      // Giả lập lấy tên người dùng từ email
      const name = storedEmail.split("@")[0];
      setUserName(name.charAt(0).toUpperCase() + name.slice(1));
    }
  }, []);

  const handleLogout = () => {
    // Xóa thông tin đăng nhập
    localStorage.removeItem("isLogin");
    localStorage.removeItem("userEmail");

    // Xóa token
    document.cookie = "token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

    // Chuyển hướng về trang đăng nhập
    router.push("/login");
  };

  return (
    <>
      <header
        className={`h-16 flex items-center justify-between bg-white px-8 fixed z-10 transition-all duration-300 ease-in-out rounded-xl shadow-md ml-4 mt-4 ${
          isSidebarCollapsed ? "left-28 right-4" : "left-72 right-4"
        }`}
      >
        <div className="flex-1 flex items-center justify-center">
          <span className="text-gray-600 font-medium text-base">
            {dateString}
          </span>
        </div>
        <div className="flex items-center gap-6">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="relative p-2 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">
                  <FiBell size={20} className="text-gray-600" />
                  <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full border-2 border-white"></span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Coming soon</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-2 focus:outline-none cursor-pointer">
                <div className="w-9 h-9 rounded-full overflow-hidden border-2 border-green-200 hover:border-green-300 transition-colors">
                  <Image
                    src="/favicon.ico"
                    alt="avatar"
                    width={36}
                    height={36}
                  />
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="flex flex-col space-y-1 p-2">
                <p className="text-sm font-medium">{userName}</p>
                <p className="text-xs text-muted-foreground">{userEmail}</p>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => setIsProfileOpen(true)}
              >
                <FiUser className="mr-2 h-4 w-4" />
                <span>Hồ sơ cá nhân</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="cursor-pointer text-red-600 focus:text-red-600"
                onClick={handleLogout}
              >
                <FiLogOut className="mr-2 h-4 w-4" />
                <span>Đăng xuất</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      {/* Dialog hồ sơ cá nhân */}
      <ProfileDialog open={isProfileOpen} onOpenChange={setIsProfileOpen} />
    </>
  );
}
