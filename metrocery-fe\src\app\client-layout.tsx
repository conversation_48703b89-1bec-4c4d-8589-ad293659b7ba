"use client";

import { usePathname } from "next/navigation";
import Sidebar from "@/components/client/Sidebar";
import Topbar from "@/components/client/Topbar";
import PageContainer from "@/components/shared/PageContainer";
import { useSidebar } from "@/contexts/SidebarContext";

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { collapsed: isSidebarCollapsed } = useSidebar();
  const isLoginPage = pathname === "/login";

  if (isLoginPage) {
    return <>{children}</>;
  }

  return (
    <>
      <Sidebar />
      <Topbar />
      <main
        className={`transition-all duration-300 ease-in-out pt-24 min-h-screen ${
          isSidebarCollapsed ? "pl-28" : "pl-72"
        }`}
      >
        <div
          className="mx-4 mb-4 p-0 overflow-hidden transition-all duration-300 ease-in-out"
          style={{ height: "calc(100vh - 7rem)" }}
        >
          <div className="w-full h-full bg-white/90 backdrop-blur-sm rounded-xl shadow-md overflow-hidden">
            <div className="p-6 h-full overflow-y-auto">
              <PageContainer>{children}</PageContainer>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
