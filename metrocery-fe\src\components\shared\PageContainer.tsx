"use client";

import React from "react";

interface PageContainerProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
}

export default function PageContainer({
  children,
  className = "",
  title,
  subtitle,
  actions,
}: PageContainerProps) {
  return (
    <div
      className={`h-full flex flex-col transition-all duration-300 ease-in-out ${className}`}
    >
      {/* Header */}
      {(title || actions) && (
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 pb-4 border-b">
          <div>
            {title && (
              <h1 className="text-2xl font-bold text-gray-800">{title}</h1>
            )}
            {subtitle && (
              <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
          {actions && <div className="flex items-center gap-2">{actions}</div>}
        </div>
      )}

      {/* Content */}
      <div className="flex-1 pb-6">{children}</div>
    </div>
  );
}
